# 📄 文档处理系统 Demo

这是一个最小化的演示应用，让您可以体验完整的文档处理功能。

## 🚀 快速开始

### 1. 环境准备

```bash
# 进入 demo 目录
cd demo

# 安装依赖
pip install -r requirements.txt

# 启动演示应用
python app.py
```

### 2. 访问应用

打开浏览器访问：http://localhost:8080

## 🎯 功能演示

### 📤 文档上传
- 支持 `.txt`, `.md` 文件上传
- 自动计算文件大小和类型
- 实时显示上传状态

### ✂️ 文档分割
- **Token 策略**：基于 Token 数量智能分割
- **字符策略**：基于字符数量分割
- 可调整分割参数
- 实时预览分割结果

### 📊 处理统计
- 分块数量统计
- 字符和 Token 统计
- 平均分块大小
- 处理时间统计

### 🔍 分块浏览
- 查看所有文档分块
- 分块内容预览
- 分块元数据显示
- 支持分页浏览

### ⚡ 异步处理
- 后台文档处理
- 处理状态实时更新
- 支持重新处理
- 自定义策略处理

## 📁 项目结构

```
demo/
├── README.md           # 说明文档
├── requirements.txt    # Python 依赖
├── app.py             # 主应用程序
├── static/            # 静态资源
│   ├── style.css      # 样式文件
│   └── script.js      # JavaScript 脚本
├── templates/         # HTML 模板
│   └── index.html     # 主页面
├── uploads/           # 上传文件存储
└── demo_documents/    # 示例文档
    ├── sample.txt     # 示例文本文件
    └── sample.md      # 示例 Markdown 文件
```

## 🎮 使用指南

### 步骤 1：上传文档
1. 点击"选择文件"按钮
2. 选择 `.txt` 或 `.md` 文件
3. 点击"上传文档"
4. 等待上传完成

### 步骤 2：选择分割策略
1. 选择分割策略：
   - **Token 策略**：适合需要精确控制 Token 数量的场景
   - **字符策略**：适合简单的字符数量控制
2. 调整参数：
   - Token 策略：设置最大 Token 数 (默认 500)
   - 字符策略：设置最大字符数 (默认 1000)

### 步骤 3：执行分割
1. 点击"开始分割"按钮
2. 观察实时处理状态
3. 查看分割结果统计

### 步骤 4：浏览结果
1. 在"分块结果"区域查看所有分块
2. 点击分块查看详细内容
3. 查看分块元数据（位置、大小、Token 数等）

### 步骤 5：重新处理
1. 修改分割参数
2. 点击"重新处理"
3. 比较不同策略的效果

## 🔧 技术特性

### 前端技术
- **HTML5**：现代化的用户界面
- **CSS3**：响应式设计和美观样式
- **JavaScript**：交互逻辑和异步请求
- **Bootstrap**：快速原型和组件

### 后端技术
- **Flask**：轻量级 Web 框架
- **文档处理引擎**：集成 engines/text_splitter
- **异步处理**：模拟后台任务处理
- **文件管理**：安全的文件上传和存储

### 核心功能
- **实时处理**：WebSocket 或轮询实现实时状态更新
- **错误处理**：完善的错误提示和异常处理
- **数据验证**：文件类型和大小验证
- **安全性**：文件上传安全检查

## 📝 示例文档

demo 包含两个示例文档供测试：

### sample.txt
```
这是一个示例文本文档，用于演示文档处理系统的功能。
文档包含多个段落和句子，可以测试不同的分割策略效果。
您可以观察 Token 策略和字符策略的不同分割结果。
```

### sample.md
```markdown
# 示例 Markdown 文档

这是一个 **Markdown** 格式的示例文档。

## 功能特性

- 支持 Markdown 语法
- 自动识别文档结构
- 智能分割处理

## 使用说明

1. 上传文档
2. 选择策略
3. 查看结果
```

## 🎯 演示场景

### 场景 1：文本文档处理
1. 上传 `demo_documents/sample.txt`
2. 使用 Token 策略 (max_tokens=100)
3. 观察分割结果和统计信息

### 场景 2：Markdown 文档处理
1. 上传 `demo_documents/sample.md`
2. 使用字符策略 (max_chars=200)
3. 比较与 Token 策略的差异

### 场景 3：参数调优
1. 使用同一文档测试不同参数
2. 比较分块数量和质量
3. 找到最适合的分割策略

### 场景 4：大文档处理
1. 上传较大的文档文件
2. 观察异步处理过程
3. 验证处理性能和稳定性

## 🔍 故障排除

### 常见问题

**Q: 上传失败怎么办？**
A: 检查文件格式（仅支持 .txt, .md）和文件大小（< 10MB）

**Q: 分割结果为空？**
A: 检查文档内容是否为空，或调整分割参数

**Q: 处理时间过长？**
A: 大文档需要更多处理时间，请耐心等待

**Q: 页面无响应？**
A: 刷新页面重试，或检查控制台错误信息

### 调试模式

启动调试模式：
```bash
python app.py --debug
```

查看详细日志：
```bash
python app.py --verbose
```

## 📞 技术支持

如果遇到问题，请检查：
1. Python 版本 >= 3.8
2. 所有依赖已正确安装
3. 端口 8080 未被占用
4. 文件权限设置正确

---

🎉 **开始体验文档处理系统的强大功能吧！**
